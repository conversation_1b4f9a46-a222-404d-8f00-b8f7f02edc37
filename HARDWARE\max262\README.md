# MAX262驱动程序 - STM32F407移植版

## 概述
本驱动程序是从STM32F103移植到STM32F407的MAX262双通道可编程滤波器驱动。MAX262是一款高性能的双通道可编程滤波器芯片，支持低通、高通、带通和带阻四种滤波模式。

## 移植说明

### 主要修改内容
1. **头文件适配**: 从`stm32f10x.h`改为`stm32f4xx.h`
2. **GPIO操作方式**: 从直接寄存器操作改为标准库函数调用
3. **端口重新分配**: 使用项目中未占用的GPIO端口
4. **延时函数适配**: 使用项目中的`delay_us()`函数

### 端口分配

#### 原STM32F103配置
- 数据线: PB8(D0), PB9(D1)
- 地址线: PB10(A0), PB11(A1), PB12(A2), PB13(A3)
- 控制线: PE14(LE), PE15(WR)

#### 新STM32F407配置
- **数据线**: PB0(D0), PB1(D1)
- **地址线**: PC0(A0), PC2(A1), PC3(A2), PC4(A3)
- **控制线**: PC5(LE), PC6(WR)

### 避免的端口冲突
移植时避开了以下已占用的端口:
- **GPIOA**: PA1(ADC1), PA5/PA6/PA7(AD9833), PA0(按键)
- **GPIOB**: PB15(LCD背光)
- **GPIOC**: PC1(ADC2)
- **GPIOD**: PD0,PD1,PD8,PD9,PD10,PD14,PD15(LCD), PD2,PD3,PD7(触摸屏), PD4,PD5(LCD控制)
- **GPIOE**: PE7-PE15(LCD), PE13(按键)
- **GPIOF**: PF0,PF1,PF2(AD9833), PF12(LCD_RS)
- **GPIOG**: PG12(LCD_CS)

## 文件结构
```
hardware/max262/
├── MAX262.h          # 头文件 - 端口定义和函数声明
├── MAX262.c          # 源文件 - 驱动实现
├── MAX262_example.c  # 使用示例
└── README.md         # 本说明文件
```

## API接口

### 初始化函数
```c
void MAX262_GPIO_Init(void);
```
- **功能**: 初始化MAX262相关的GPIO端口
- **参数**: 无
- **返回**: 无
- **说明**: 使用前必须先调用此函数

### 滤波器配置函数
```c
void Filter1(uint8_t mode, float q);
void Filter2(uint8_t mode, float q);
```
- **功能**: 配置滤波器1/2的模式和Q值
- **参数**: 
  - `mode`: 滤波器模式 (0=低通, 1=高通, 2=带通, 3=带阻)
  - `q`: 品质因数 (建议范围: 0.5~100)
- **返回**: 无

### 辅助函数
```c
uint8_t Qn(float q);
```
- **功能**: 将Q值转换为MAX262的控制字
- **参数**: `q` - 品质因数
- **返回**: 8位控制字

## 使用方法

### 1. 基本初始化
```c
#include "MAX262.h"

// 全局变量 - 频率控制字
int Fn = 63;  // 对应139.8kHz时钟频率

int main(void)
{
    // 系统初始化
    delay_init(168);
    
    // 初始化MAX262 GPIO
    MAX262_GPIO_Init();
    
    // 配置滤波器
    Filter1(0, 2.0f);  // 滤波器1: 低通, Q=2.0
    Filter2(1, 2.0f);  // 滤波器2: 高通, Q=2.0
    
    while(1)
    {
        // 主循环
    }
}
```

### 2. 动态配置示例
```c
// 动态切换滤波器模式
void switch_filter_mode(void)
{
    Filter1(0, 1.0f);   // 低通滤波器
    delay_ms(1000);
    
    Filter1(1, 2.0f);   // 高通滤波器  
    delay_ms(1000);
    
    Filter1(2, 5.0f);   // 带通滤波器
    delay_ms(1000);
    
    Filter1(3, 10.0f);  // 带阻滤波器
}
```

## 技术参数

### 滤波器模式
- **0**: 低通滤波器 (Low-Pass)
- **1**: 高通滤波器 (High-Pass)  
- **2**: 带通滤波器 (Band-Pass)
- **3**: 带阻滤波器 (Band-Stop/Notch)

### Q值范围
- **推荐范围**: 0.5 ~ 100
- **典型值**: 1.0 ~ 10.0
- **高Q值**: 适用于窄带滤波
- **低Q值**: 适用于宽带滤波

### 时钟频率
- **默认**: 139.8kHz (Fn = 63)
- **可调**: 通过修改全局变量`Fn`调整

## 注意事项

1. **初始化顺序**: 必须先调用`MAX262_GPIO_Init()`
2. **延时函数**: 确保`delay.h`中的延时函数正确配置
3. **时钟设置**: 根据实际硬件调整`Fn`变量
4. **端口冲突**: 确认所选端口未被其他模块占用
5. **电源供电**: 确保MAX262芯片正常供电
6. **信号完整性**: 注意PCB布线和信号完整性

## 故障排除

### 常见问题
1. **滤波器无响应**: 检查GPIO初始化和端口连接
2. **滤波效果异常**: 检查Q值设置和模式配置
3. **编译错误**: 确认头文件包含和函数声明

### 调试建议
1. 使用示例代码`MAX262_example.c`进行测试
2. 通过串口输出调试信息
3. 使用示波器检查控制信号时序
4. 验证GPIO端口电平变化

## 版本历史
- **V2.0** (2024-08-03): STM32F407移植版，重新分配GPIO端口
- **V1.0** (2018-05-24): 原STM32F103版本
