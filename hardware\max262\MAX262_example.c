//-----------------------------------------------------------------
// MAX262使用示例 - STM32F407
// 文件名: MAX262_example.c
// 作  者: 适配STM32F407
// 日  期: 2024-08-03
// 说  明: 演示如何在主程序中使用MAX262驱动
//-----------------------------------------------------------------

#include "MAX262.h"
#include "delay.h"
#include <stdio.h>

// 注意：Fn变量已在MAX262.c中定义，这里不需要重复定义

//-----------------------------------------------------------------
// MAX262初始化示例
//-----------------------------------------------------------------
void MAX262_Example_Init(void)
{
    // 初始化GPIO端口
    MAX262_GPIO_Init();
    
    printf("MAX262 GPIO初始化完成\r\n");
    printf("使用的端口配置:\r\n");
    printf("  数据线: PB0(D0), PB1(D1)\r\n");
    printf("  地址线: PC0(A0), PC2(A1), PC3(A2), PC4(A3)\r\n");
    printf("  控制线: PC5(LE), PC6(WR)\r\n");
}

//-----------------------------------------------------------------
// 配置滤波器示例
//-----------------------------------------------------------------
void MAX262_Example_Config(void)
{
    // 滤波器模式定义
    // 0: 低通滤波器
    // 1: 高通滤波器  
    // 2: 带通滤波器
    // 3: 带阻滤波器
    
    float q_value = 2.0f;  // Q值设置为2.0
    
    printf("配置MAX262滤波器...\r\n");
    
    // 配置滤波器1为低通滤波器，Q=2.0
    Filter1(0, q_value);
    printf("滤波器1: 低通滤波器, Q=%.1f\r\n", q_value);
    
    delay_ms(10);  // 短暂延时
    
    // 配置滤波器2为高通滤波器，Q=2.0
    Filter2(1, q_value);
    printf("滤波器2: 高通滤波器, Q=%.1f\r\n", q_value);
    
    printf("MAX262配置完成\r\n");
}

//-----------------------------------------------------------------
// 动态调整滤波器参数示例
//-----------------------------------------------------------------
void MAX262_Example_Dynamic_Config(void)
{
    uint8_t mode;
    float q_values[] = {1.0f, 2.0f, 5.0f, 10.0f};
    uint8_t modes[] = {0, 1, 2, 3};  // 低通、高通、带通、带阻
    char* mode_names[] = {"低通", "高通", "带通", "带阻"};
    
    printf("动态配置MAX262滤波器示例...\r\n");
    
    // 循环测试不同的滤波器配置
    for(int i = 0; i < 4; i++)
    {
        mode = modes[i];
        
        printf("\r\n--- 配置 %d ---\r\n", i+1);
        printf("滤波器1: %s滤波器, Q=%.1f\r\n", mode_names[mode], q_values[i]);
        Filter1(mode, q_values[i]);
        
        delay_ms(100);
        
        printf("滤波器2: %s滤波器, Q=%.1f\r\n", mode_names[mode], q_values[i]);
        Filter2(mode, q_values[i]);
        
        delay_ms(1000);  // 等待1秒观察效果
    }
    
    printf("\r\n动态配置测试完成\r\n");
}

//-----------------------------------------------------------------
// 在主程序中的使用方法
//-----------------------------------------------------------------
/*
int main(void)
{
    // 系统初始化
    delay_init(168);  // 初始化延时函数

    // 初始化MAX262
    MAX262_Example_Init();

    // 基本配置示例
    MAX262_Example_Config();

    // 动态配置示例
    MAX262_Example_Dynamic_Config();

    while(1)
    {
        // 主循环
        delay_ms(1000);
    }
}
*/

//-----------------------------------------------------------------
// 动态频率调整示例
//-----------------------------------------------------------------
void MAX262_Example_FrequencyAdjust(void)
{
    float frequencies[] = {500.0f, 1000.0f, 2000.0f, 5000.0f};
    uint8_t freq_count = sizeof(frequencies) / sizeof(frequencies[0]);

    printf("动态频率调整示例...\r\n");

    for(int i = 0; i < freq_count; i++)
    {
        printf("\r\n--- 频率设置 %d ---\r\n", i+1);

        // 设置新的中心频率
        SetFilterCenterFrequency(frequencies[i]);

        // 获取实际设置的频率
        float actual_freq = GetCurrentCenterFrequency();
        printf("实际中心频率: %.1fHz\r\n", actual_freq);

        // 重新配置滤波器（频率改变后必须重新配置）
        Filter1(2, 2.0f);  // 带通滤波器, Q=2.0
        Filter2(2, 2.0f);  // 带通滤波器, Q=2.0

        printf("滤波器已配置为带通模式, Q=2.0\r\n");

        delay_ms(2000);  // 等待2秒观察效果
    }

    printf("\r\n频率调整测试完成\r\n");
}

//-----------------------------------------------------------------
// 注意事项:
// 1. 在使用前需要先调用MAX262_GPIO_Init()初始化GPIO
// 2. 确保delay.h中的延时函数已正确配置
// 3. Fn变量已在MAX262.c中定义，默认值为63(对应139.8kHz)
// 4. 使用SetFilterCenterFrequency()函数动态调整中心频率
// 5. 频率改变后必须重新调用Filter1/Filter2函数
// 6. Q值范围通常在0.5到100之间
// 7. 模式值: 0=低通, 1=高通, 2=带通, 3=带阻
// 8. 中心频率范围: 约55Hz ~ 2796Hz (基于139.8kHz时钟)
//-----------------------------------------------------------------
