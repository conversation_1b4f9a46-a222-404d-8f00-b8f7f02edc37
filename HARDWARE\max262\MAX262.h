//-----------------------------------------------------------------
// MAX262驱动头文件 - 适配STM32F407
// 头文件名: MAX262.h
// 作    者: 修改适配STM32F407
// 编写时间: 2024-08-03
// 修改说明: 从STM32F103移植到STM32F407，使用未占用的GPIO端口
//-----------------------------------------------------------------

#ifndef _MAX262_H
#define _MAX262_H

#include "stm32f4xx.h"
#include "sys.h"

//-----------------------------------------------------------------
// GPIO端口定义 - 使用未被占用的端口
//-----------------------------------------------------------------
// 数据线D0, D1 - 使用GPIOB的未占用端口
#define	D0_PORT			GPIOB
#define	D0_PIN			GPIO_Pin_0
#define D0_L			GPIO_ResetBits(D0_PORT, D0_PIN)
#define D0_H			GPIO_SetBits(D0_PORT, D0_PIN)

#define	D1_PORT			GPIOB
#define	D1_PIN			GPIO_Pin_1
#define	D1_L			GPIO_ResetBits(D1_PORT, D1_PIN)
#define	D1_H			GPIO_SetBits(D1_PORT, D1_PIN)

// 地址线A0-A3 - 使用GPIOC的未占用端口
#define	A0_PORT		    GPIOC
#define	A0_PIN		    GPIO_Pin_0
#define	A0_L	        GPIO_ResetBits(A0_PORT, A0_PIN)
#define	A0_H	        GPIO_SetBits(A0_PORT, A0_PIN)
#define A0_IS_L	        (GPIO_ReadInputDataBit(A0_PORT, A0_PIN) == Bit_RESET)

#define	A1_PORT		    GPIOC
#define	A1_PIN		    GPIO_Pin_2
#define	A1_L	        GPIO_ResetBits(A1_PORT, A1_PIN)
#define	A1_H	        GPIO_SetBits(A1_PORT, A1_PIN)
#define A1_IS_H		    (GPIO_ReadInputDataBit(A1_PORT, A1_PIN) == Bit_SET)

#define	A2_PORT			GPIOC
#define	A2_PIN			GPIO_Pin_3
#define	A2_L	        GPIO_ResetBits(A2_PORT, A2_PIN)
#define	A2_H	        GPIO_SetBits(A2_PORT, A2_PIN)

#define	A3_PORT		    GPIOC
#define	A3_PIN		    GPIO_Pin_4
#define	A3_L	        GPIO_ResetBits(A3_PORT, A3_PIN)
#define	A3_H	        GPIO_SetBits(A3_PORT, A3_PIN)

// 控制信号LE, WR - 使用GPIOC的未占用端口
#define	LE_PORT		    GPIOC
#define	LE_PIN		    GPIO_Pin_5
#define	LE_L	        GPIO_ResetBits(LE_PORT, LE_PIN)
#define	LE_H	        GPIO_SetBits(LE_PORT, LE_PIN)

#define	WR_PORT		    GPIOC
#define	WR_PIN		    GPIO_Pin_6
#define	WR_L	        GPIO_ResetBits(WR_PORT, WR_PIN)
#define	WR_H	        GPIO_SetBits(WR_PORT, WR_PIN)

//-----------------------------------------------------------------
// MAX262相关常数定义
//-----------------------------------------------------------------
// #define Fn   63   //139.8k时钟频率
#define NF1 139.8
#define NF2 40.48

//-----------------------------------------------------------------
// 函数声明
//-----------------------------------------------------------------
void MAX262_GPIO_Init(void);
void Filter1(uint8_t mode, float q);
void Filter2(uint8_t mode, float q);
uint8_t Qn(float q);

#endif

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
