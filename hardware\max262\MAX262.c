//-----------------------------------------------------------------
// 程序描述:
//		MAX262驱动程序 - 适配STM32F407
// 作    者: 修改适配STM32F407
// 开始日期: 2024-08-03
// 完成日期: 2024-08-03
// 修改说明: 从STM32F103移植到STM32F407
// 版    本: V2.0
//   - V2.0: 适配STM32F407，使用未占用的GPIO端口
// 测试工具: STM32F407开发板
// 说    明: MAX262双通道可编程滤波器驱动
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// 头文件包含
//-----------------------------------------------------------------
#include "stm32f4xx.h"
#include "MAX262.h"
#include "delay.h"
#include <math.h>

// 频率控制字定义 - 对应139.8kHz时钟频率
int Fn = 63;

//-----------------------------------------------------------------
// 功能程序
//-----------------------------------------------------------------

//-----------------------------------------------------------------
// void MAX262_GPIO_Init(void)
//-----------------------------------------------------------------
// 函数功能: 初始化MAX262相关的GPIO端口
// 输入参数: 无
// 返 回 值: 无
// 全局变量: 无
// 注意事项: 适配STM32F407，使用未占用的GPIO端口
//-----------------------------------------------------------------
void MAX262_GPIO_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;

    // 使能GPIOB和GPIOC时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOB | RCC_AHB1Periph_GPIOC, ENABLE);

    // 配置数据线D0, D1 (GPIOB)
    GPIO_InitStructure.GPIO_Pin = D0_PIN | D1_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(D0_PORT, &GPIO_InitStructure);

    // 配置地址线A0-A3和控制线LE, WR (GPIOC)
    GPIO_InitStructure.GPIO_Pin = A0_PIN | A1_PIN | A2_PIN | A3_PIN | LE_PIN | WR_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(A0_PORT, &GPIO_InitStructure);

    // 初始化引脚状态
    D0_L;
    D1_L;
    A0_L;
    A1_L;
    A2_L;
    A3_L;
    LE_L;
    WR_H;
}


//-----------------------------------------------------------------
// uint8_t Qn(float q)
//-----------------------------------------------------------------
// 函数功能: 根据Q值计算控制字N
// 输入参数: Q值
// 返 回 值: 控制字N
// 全局变量: 无
// 注意事项: 参考MAX262芯片手册中的公式
//-----------------------------------------------------------------
uint8_t Qn(float q)                     // 品质因数计算控制字
{
    uint8_t temp;
    temp = 128 - (64 / q);             // 参考MAX262芯片手册中的公式
    return temp;
}

//-----------------------------------------------------------------
// void Filter1(uint8_t mode, float q)
//-----------------------------------------------------------------
// 函数功能: 滤波器1的模式、频率以及Q值配置
// 输入参数: 模式 mode, 停止/启动 频率 f, Q值 q
// 返 回 值: 无
// 全局变量: 无
// 注意事项: 适配STM32F407的GPIO操作方式
//-----------------------------------------------------------------
void Filter1(uint8_t mode, float q)
{
	uint8_t i;
	uint8_t a = 0x03;
	uint8_t sq;
	i = sq = 0;

	sq = Qn(q);                     // 计算Q对应的控制字N
	LE_H;                           // 使能锁存器
	delay_us(1);                    // 使用适配的延时函数
	WR_H;                           // 写端口置高
	delay_us(1);

	// 参考MAX262芯片手册 P15 Table4

	// 清除数据线和地址线
	D0_L; D1_L;
	A0_L; A1_L; A2_L; A3_L;
	delay_us(1);
	WR_L;                           // 写端口置低
	delay_us(1);

	// 将模式数据传送给D1, D0
	if (mode & 0x01) D0_H; else D0_L;
	if (mode & 0x02) D1_H; else D1_L;
	delay_us(1);
	WR_H;                           // 写端口置高
	delay_us(1);

	// 写入频率控制字 (3个字节)
	for(i = 0; i < 3; i++)
	{
		// 清除数据线和地址线
		D0_L; D1_L;
		A0_L; A1_L; A2_L; A3_L;

		// 设置地址 (i+1)
		if ((i+1) & 0x01) A0_H;
		if ((i+1) & 0x02) A1_H;
		if ((i+1) & 0x04) A2_H;
		if ((i+1) & 0x08) A3_H;

		delay_us(1);
		WR_L;                             // 写信号置低
		delay_us(1);

		// 将频率控制字的相应位写入D1, D0
		if (Fn & (1 << (2*i))) D0_H;
		if (Fn & (1 << (2*i+1))) D1_H;

		delay_us(1);
		WR_H;                             // 写信号置高
		delay_us(1);
	}

	// 写入Q值控制字 (4个字节)
	for(i = 0; i < 4; i++)
	{
		// 清除数据线和地址线
		D0_L; D1_L;
		A0_L; A1_L; A2_L; A3_L;

		// 设置地址 (i+4)
		uint8_t addr = i + 4;
		if (addr & 0x01) A0_H;
		if (addr & 0x02) A1_H;
		if (addr & 0x04) A2_H;
		if (addr & 0x08) A3_H;

		delay_us(1);
		WR_L;                             // 写信号置低
		delay_us(1);

		// 将Q值控制字的相应位写入D1, D0
		if (sq & (1 << (2*i))) D0_H;
		if (sq & (1 << (2*i+1))) D1_H;

		delay_us(1);
		WR_H;                             // 写信号置高
		delay_us(1);
	}
}

//-----------------------------------------------------------------
// void Filter2(uint8_t mode, float q)
//-----------------------------------------------------------------
// 函数功能: 滤波器2的模式、频率以及Q值配置
// 输入参数: 模式 mode, 停止/启动 频率 f, Q值 q
// 返 回 值: 无
// 全局变量: 无
// 注意事项: 适配STM32F407的GPIO操作方式
//-----------------------------------------------------------------
void Filter2(uint8_t mode, float q)
{
	uint8_t i;
	uint8_t sq;
	i = sq = 0;

	sq = Qn(q);                     // 计算Q对应的控制字N
	LE_H;                           // 使能锁存器
	delay_us(1);
	WR_H;                           // 写端口置高
	delay_us(1);

	// 清除数据线和地址线
	D0_L; D1_L;
	A0_L; A1_L; A2_L; A3_L;

	// 设置地址为8 (滤波器2的模式寄存器)
	A3_H;  // 8 = 1000b
	delay_us(1);
	WR_L;                           // 写端口置低
	delay_us(1);

	// 将模式数据传送给D1, D0
	if (mode & 0x01) D0_H; else D0_L;
	if (mode & 0x02) D1_H; else D1_L;
	delay_us(1);
	WR_H;                           // 写端口置高
	delay_us(1);

	// 写入频率控制字 (3个字节，地址9-11)
	for(i = 0; i < 3; i++)
	{
		// 清除数据线和地址线
		D0_L; D1_L;
		A0_L; A1_L; A2_L; A3_L;

		// 设置地址 (i+9)
		uint8_t addr = i + 9;
		if (addr & 0x01) A0_H;
		if (addr & 0x02) A1_H;
		if (addr & 0x04) A2_H;
		if (addr & 0x08) A3_H;

		delay_us(1);
		WR_L;                             // 写信号置低
		delay_us(1);

		// 将频率控制字的相应位写入D1, D0
		if (Fn & (1 << (2*i))) D0_H;
		if (Fn & (1 << (2*i+1))) D1_H;

		delay_us(1);
		WR_H;                             // 写信号置高
		delay_us(1);
	}

	// 写入Q值控制字 (4个字节，地址12-15)
	for(i = 0; i < 4; i++)
	{
		// 清除数据线和地址线
		D0_L; D1_L;
		A0_L; A1_L; A2_L; A3_L;

		// 设置地址 (i+12)
		uint8_t addr = i + 12;
		if (addr & 0x01) A0_H;
		if (addr & 0x02) A1_H;
		if (addr & 0x04) A2_H;
		if (addr & 0x08) A3_H;

		delay_us(1);
		WR_L;                             // 写信号置低
		delay_us(1);

		// 将Q值控制字的相应位写入D1, D0
		if (sq & (1 << (2*i))) D0_H;
		if (sq & (1 << (2*i+1))) D1_H;

		delay_us(1);
		WR_H;                             // 写信号置高
		delay_us(1);
	}
}

//-----------------------------------------------------------------
// void SetFilterCenterFrequency(float target_freq_hz)
//-----------------------------------------------------------------
// 函数功能: 设置滤波器中心频率
// 输入参数: target_freq_hz - 目标中心频率 (Hz)
// 返 回 值: 无
// 全局变量: 修改全局变量Fn
// 注意事项: 设置后需要重新调用Filter1/Filter2函数
//-----------------------------------------------------------------
void SetFilterCenterFrequency(float target_freq_hz)
{
    // MAX262的中心频率计算公式：
    // f0 = fCLK / (50 * Fn)
    // 因此：Fn = fCLK / (50 * f0)

    float fCLK = 139800.0f;  // 139.8kHz时钟频率
    int new_Fn = (int)(fCLK / (50.0f * target_freq_hz));

    // 限制Fn的范围 (1-255)
    if (new_Fn < 1) new_Fn = 1;
    if (new_Fn > 255) new_Fn = 255;

    Fn = new_Fn;

    printf("设置中心频率: %.1fHz, Fn=%d\r\n", target_freq_hz, Fn);
}

//-----------------------------------------------------------------
// float GetCurrentCenterFrequency(void)
//-----------------------------------------------------------------
// 函数功能: 获取当前滤波器中心频率
// 输入参数: 无
// 返 回 值: 当前中心频率 (Hz)
// 全局变量: 读取全局变量Fn
// 注意事项: 基于当前Fn值计算
//-----------------------------------------------------------------
float GetCurrentCenterFrequency(void)
{
    float fCLK = 139800.0f;  // 139.8kHz时钟频率
    return fCLK / (50.0f * Fn);
}

//-----------------------------------------------------------------
// End Of File
//-----------------------------------------------------------------
